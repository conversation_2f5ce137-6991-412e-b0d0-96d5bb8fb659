"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRefs = void 0;
const Options_js_1 = require("./Options.js");
const getRefs = (options) => {
    const _options = (0, Options_js_1.getDefaultOptions)(options);
    const currentPath = _options.name !== undefined
        ? [..._options.basePath, _options.definitionPath, _options.name]
        : _options.basePath;
    return {
        ..._options,
        currentPath: currentPath,
        propertyPath: undefined,
        seen: new Map(Object.entries(_options.definitions).map(([name, def]) => [
            def._def,
            {
                def: def._def,
                path: [..._options.basePath, _options.definitionPath, name],
                // Resolution of references will be forced even though seen, so it's ok that the schema is undefined here for now.
                jsonSchema: undefined,
            },
        ])),
    };
};
exports.getRefs = getRefs;
