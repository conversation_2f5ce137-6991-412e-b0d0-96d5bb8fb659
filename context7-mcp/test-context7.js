// 测试Context7 MCP
const { execSync } = require('child_process');

console.log('测试Context7 MCP服务器...');

try {
  // 测试resolve-library-id工具
  console.log('测试resolve-library-id工具：');
  const resolveResult = execSync('npx @upstash/context7-mcp resolve-library-id --libraryName="react"', {
    encoding: 'utf-8'
  });
  console.log('结果:', resolveResult);
  
  // 测试get-library-docs工具
  console.log('\n测试get-library-docs工具：');
  const docsResult = execSync('npx @upstash/context7-mcp get-library-docs --context7CompatibleLibraryID="/facebook/react" --tokens=1000', {
    encoding: 'utf-8'
  });
  console.log('结果长度:', docsResult.length, '字节');
  console.log('结果前200个字符:', docsResult.substring(0, 200) + '...');
} catch (error) {
  console.error('测试失败:', error.message);
  if (error.stdout) console.log('标准输出:', error.stdout.toString());
  if (error.stderr) console.log('错误输出:', error.stderr.toString());
}